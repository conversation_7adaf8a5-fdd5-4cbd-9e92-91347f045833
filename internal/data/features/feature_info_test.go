package features

import (
	"math"
	"testing"
)

// TestFeatureTypeString tests the String method of FeatureType.
//
// Args: None (standard Go test function)
// Performance: O(1) constant time string comparison tests
// Relationships: Tests FeatureType.String() method implementation
// Side effects: None (read-only testing)
func TestFeatureTypeString(t *testing.T) {
	tests := []struct {
		name     string
		ft       FeatureType
		expected string
	}{
		{
			name:     "IntegerFeature",
			ft:       IntegerFeature,
			expected: "integer",
		},
		{
			name:     "FloatFeature",
			ft:       FloatFeature,
			expected: "float",
		},
		{
			name:     "StringFeature",
			ft:       StringFeature,
			expected: "string",
		},
		{
			name:     "Unknown feature type",
			ft:       FeatureType(999), // Invalid type
			expected: "unknown",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.ft.String()
			if result != tt.expected {
				t.Errorf("Expected %q, got %q", tt.expected, result)
			}
		})
	}
}

// TestNewFeatureInfo tests the NewFeatureInfo constructor for all feature types.
//
// Args: None (standard Go test function)
// Performance: O(1) initialization tests for each feature type
// Relationships: Tests NewFeatureInfo() constructor and type-specific initialization
// Side effects: Creates FeatureInfo instances for testing
func TestNewFeatureInfo(t *testing.T) {
	tests := []struct {
		name         string
		featureName  string
		featureType  FeatureType
		originalType string
	}{
		{
			name:         "integer feature",
			featureName:  "age",
			featureType:  IntegerFeature,
			originalType: "numeric",
		},
		{
			name:         "float feature",
			featureName:  "salary",
			featureType:  FloatFeature,
			originalType: "numeric",
		},
		{
			name:         "string feature",
			featureName:  "department",
			featureType:  StringFeature,
			originalType: "nominal",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fi := NewFeatureInfo(tt.featureName, tt.featureType, tt.originalType)

			if fi == nil {
				t.Fatal("NewFeatureInfo returned nil")
			}
			if fi.Name != tt.featureName {
				t.Errorf("Expected Name=%q, got %q", tt.featureName, fi.Name)
			}
			if fi.Type != tt.featureType {
				t.Errorf("Expected Type=%v, got %v", tt.featureType, fi.Type)
			}
			if fi.OriginalType != tt.originalType {
				t.Errorf("Expected OriginalType=%q, got %q", tt.originalType, fi.OriginalType)
			}

			// Test type-specific distribution initialization
			switch tt.featureType {
			case StringFeature:
				if fi.CategoricalDistribution == nil {
					t.Error("CategoricalDistribution should be initialized for StringFeature")
				}
				if len(fi.CategoricalDistribution) != 0 {
					t.Error("CategoricalDistribution should be empty initially")
				}
				if fi.NumericalDistribution != nil {
					t.Error("NumericalDistribution should be nil for StringFeature")
				}
			case IntegerFeature, FloatFeature:
				if fi.NumericalDistribution == nil {
					t.Error("NumericalDistribution should be initialized for numerical features")
				}
				if fi.NumericalDistribution.GetTotalCount() != 0 {
					t.Error("NumericalDistribution should be empty initially")
				}
				if fi.CategoricalDistribution != nil {
					t.Error("CategoricalDistribution should be nil for numerical features")
				}
			}
		})
	}
}

// TestFeatureInfoIsNumerical tests the IsNumerical method.
//
// Args: None (standard Go test function)
// Performance: O(1) type checking tests
// Relationships: Tests IsNumerical() method for all feature types
// Side effects: None (read-only testing)
func TestFeatureInfoIsNumerical(t *testing.T) {
	tests := []struct {
		name        string
		featureType FeatureType
		expected    bool
	}{
		{
			name:        "IntegerFeature is numerical",
			featureType: IntegerFeature,
			expected:    true,
		},
		{
			name:        "FloatFeature is numerical",
			featureType: FloatFeature,
			expected:    true,
		},
		{
			name:        "StringFeature is not numerical",
			featureType: StringFeature,
			expected:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fi := NewFeatureInfo("test", tt.featureType, "test")
			result := fi.IsNumerical()
			if result != tt.expected {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}

// TestFeatureInfoIsCategorical tests the IsCategorical method.
//
// Args: None (standard Go test function)
// Performance: O(1) type checking tests
// Relationships: Tests IsCategorical() method for all feature types
// Side effects: None (read-only testing)
func TestFeatureInfoIsCategorical(t *testing.T) {
	tests := []struct {
		name        string
		featureType FeatureType
		expected    bool
	}{
		{
			name:        "IntegerFeature is not categorical",
			featureType: IntegerFeature,
			expected:    false,
		},
		{
			name:        "FloatFeature is not categorical",
			featureType: FloatFeature,
			expected:    false,
		},
		{
			name:        "StringFeature is categorical",
			featureType: StringFeature,
			expected:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fi := NewFeatureInfo("test", tt.featureType, "test")
			result := fi.IsCategorical()
			if result != tt.expected {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}

// TestFeatureInfoAddValueString tests the AddValue method for string features.
//
// Args: None (standard Go test function)
// Performance: O(1) per addition, tests categorical distribution updates
// Relationships: Tests AddValue() method with string values and categorical distribution
// Side effects: Modifies FeatureInfo categorical distribution for testing
func TestFeatureInfoAddValueString(t *testing.T) {
	fi := NewFeatureInfo("test", StringFeature, "nominal")

	// Add some values
	fi.AddValue("value1")
	fi.AddValue("value2")
	fi.AddValue("value1") // Duplicate
	fi.AddValue("value3")
	fi.AddValue("value1") // Another duplicate

	// Check categorical distribution
	expected := map[string]int{
		"value1": 3,
		"value2": 1,
		"value3": 1,
	}

	for key, expectedCount := range expected {
		if actualCount := fi.CategoricalDistribution[key]; actualCount != expectedCount {
			t.Errorf("Expected count %d for %q, got %d", expectedCount, key, actualCount)
		}
	}

	// Verify total samples
	if total := fi.GetTotalSamples(); total != 5 {
		t.Errorf("Expected 5 total samples, got %d", total)
	}

	// Verify unique count
	if unique := fi.GetUniqueValueCount(); unique != 3 {
		t.Errorf("Expected 3 unique values, got %d", unique)
	}
}

// TestFeatureInfoAddValueNumerical tests the AddValue method for numerical features.
//
// Args: None (standard Go test function)
// Performance: O(1) per addition, tests numerical distribution updates
// Relationships: Tests AddValue() method with int64/float64 values and numerical distribution
// Side effects: Modifies FeatureInfo numerical distribution for testing
func TestFeatureInfoAddValueNumerical(t *testing.T) {
	// Test integer feature
	intFi := NewFeatureInfo("age", IntegerFeature, "numeric")
	intFi.AddValue(int64(25))
	intFi.AddValue(int64(30))
	intFi.AddValue(int64(25)) // Duplicate

	if total := intFi.GetTotalSamples(); total != 3 {
		t.Errorf("Expected 3 total samples for integer feature, got %d", total)
	}
	if unique := intFi.GetUniqueValueCount(); unique != 2 {
		t.Errorf("Expected 2 unique values for integer feature, got %d", unique)
	}
	if count := intFi.NumericalDistribution.GetCount(int64(25)); count != 2 {
		t.Errorf("Expected count 2 for int64(25), got %d", count)
	}

	// Test float feature
	floatFi := NewFeatureInfo("salary", FloatFeature, "numeric")
	floatFi.AddValue(float64(50000.5))
	floatFi.AddValue(float64(75000.0))
	floatFi.AddValue(float64(50000.5)) // Duplicate

	if total := floatFi.GetTotalSamples(); total != 3 {
		t.Errorf("Expected 3 total samples for float feature, got %d", total)
	}
	if unique := floatFi.GetUniqueValueCount(); unique != 2 {
		t.Errorf("Expected 2 unique values for float feature, got %d", unique)
	}
	if count := floatFi.NumericalDistribution.GetCount(float64(50000.5)); count != 2 {
		t.Errorf("Expected count 2 for float64(50000.5), got %d", count)
	}
}

// TestFeatureInfoGetRangeCount tests the GetRangeCount method for numerical features.
//
// Args: None (standard Go test function)
// Performance: O(log n) binary search tests with lazy cache rebuilds
// Relationships: Tests GetRangeCount() method and numerical distribution range queries
// Side effects: May trigger sorted cache rebuild in numerical distribution
func TestFeatureInfoGetRangeCount(t *testing.T) {
	fi := NewFeatureInfo("temperature", FloatFeature, "numeric")

	// Add temperature values
	temperatures := []float64{15.0, 20.0, 25.0, 30.0, 35.0, 40.0}
	for _, temp := range temperatures {
		fi.AddValue(temp)
	}

	// Test range queries
	tests := []struct {
		name     string
		min      float64
		max      float64
		expected int
	}{
		{
			name:     "Range covering all values",
			min:      0.0,
			max:      50.0,
			expected: 6,
		},
		{
			name:     "Range covering middle values",
			min:      20.0,
			max:      35.0,
			expected: 4, // 20, 25, 30, 35
		},
		{
			name:     "Range covering no values",
			min:      50.0,
			max:      60.0,
			expected: 0,
		},
		{
			name:     "Range with single value",
			min:      25.0,
			max:      25.0,
			expected: 1, // 25.0
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			count := fi.GetRangeCount(tt.min, tt.max)
			if count != tt.expected {
				t.Errorf("Expected range count %d for range [%.1f, %.1f], got %d", tt.expected, tt.min, tt.max, count)
			}
		})
	}
}

// TestFeatureInfoGetSortedNumericalValues tests the GetSortedNumericalValues method.
//
// Args: None (standard Go test function)
// Performance: O(n log n) if cache rebuild needed, O(n) for copy operation
// Relationships: Tests GetSortedNumericalValues() method and sorted value retrieval
// Side effects: May trigger sorted cache rebuild in numerical distribution
func TestFeatureInfoGetSortedNumericalValues(t *testing.T) {
	fi := NewFeatureInfo("values", IntegerFeature, "numeric")

	// Add values in random order
	values := []int64{30, 10, 25, 10, 40, 25, 15} // Duplicates included
	for _, val := range values {
		fi.AddValue(val)
	}

	// Get sorted values
	sortedValues := fi.GetSortedNumericalValues()

	// Expected sorted values (with duplicates): [10, 10, 15, 25, 25, 30, 40]
	expected := []float64{10.0, 10.0, 15.0, 25.0, 25.0, 30.0, 40.0}

	if len(sortedValues) != len(expected) {
		t.Errorf("Expected %d sorted values, got %d", len(expected), len(sortedValues))
	}

	for i, expectedVal := range expected {
		if i < len(sortedValues) && sortedValues[i] != expectedVal {
			t.Errorf("Expected sorted value %.1f at index %d, got %.1f", expectedVal, i, sortedValues[i])
		}
	}
}

// TestFeatureInfoGetMostCommonValue tests the GetMostCommonValue method for all feature types.
//
// Args: None (standard Go test function)
// Performance: O(n) iteration through distributions
// Relationships: Tests GetMostCommonValue() method for categorical and numerical features
// Side effects: None (read-only testing)
func TestFeatureInfoGetMostCommonValue(t *testing.T) {
	// Test string feature
	stringFi := NewFeatureInfo("color", StringFeature, "nominal")

	// Test empty distribution
	value, count := stringFi.GetMostCommonValue()
	if value != nil || count != 0 {
		t.Errorf("Expected nil value and 0 count for empty string distribution, got %v and %d", value, count)
	}

	// Add values with different frequencies
	stringFi.AddValue("rare")
	stringFi.AddValue("common")
	stringFi.AddValue("common")
	stringFi.AddValue("common")
	stringFi.AddValue("medium")
	stringFi.AddValue("medium")

	value, count = stringFi.GetMostCommonValue()
	if value != "common" || count != 3 {
		t.Errorf("Expected 'common' with count 3, got %v with count %d", value, count)
	}

	// Test integer feature
	intFi := NewFeatureInfo("score", IntegerFeature, "numeric")
	intFi.AddValue(int64(85))
	intFi.AddValue(int64(90))
	intFi.AddValue(int64(85))
	intFi.AddValue(int64(85))
	intFi.AddValue(int64(95))

	value, count = intFi.GetMostCommonValue()
	if value != int64(85) || count != 3 {
		t.Errorf("Expected int64(85) with count 3, got %v with count %d", value, count)
	}
}

// TestNumericalDistributionFloatPrecision tests handling of float precision edge cases.
//
// Args: None (standard Go test function)
// Performance: O(1) per addition, tests float precision handling
// Relationships: Tests NumericalDistribution with problematic float values
// Side effects: Modifies numerical distribution for precision testing
func TestNumericalDistributionFloatPrecision(t *testing.T) {
	fi := NewFeatureInfo("precision_test", FloatFeature, "numeric")

	// Test values that actually have different floating-point representations
	val1 := 0.1 + 0.2 + 0.0000000000000001 // Slightly different from 0.3
	val2 := 0.3                            // Exact 0.3
	fi.AddValue(val1)
	fi.AddValue(val2)
	fi.AddValue(val1) // Add val1 again

	// These should be treated as different values due to precision
	uniqueCount := fi.GetUniqueValueCount()
	if uniqueCount != 2 {
		t.Errorf("Expected 2 unique values due to float precision, got %d", uniqueCount)
	}

	totalCount := fi.GetTotalSamples()
	if totalCount != 3 {
		t.Errorf("Expected 3 total samples, got %d", totalCount)
	}

	// Test NaN and infinity handling
	fi.AddValue(math.NaN())
	fi.AddValue(math.Inf(1))
	fi.AddValue(math.Inf(-1))

	if totalCount := fi.GetTotalSamples(); totalCount != 6 {
		t.Errorf("Expected 6 total samples after adding special values, got %d", totalCount)
	}

	// Get sorted values to ensure no panics with special values
	sortedValues := fi.GetSortedNumericalValues()
	if len(sortedValues) != 6 {
		t.Errorf("Expected 6 sorted values, got %d", len(sortedValues))
	}
}

// BenchmarkFeatureInfoAddValueString benchmarks the AddValue method for string features.
//
// Args: *testing.B for benchmarking
// Performance: Measures O(1) hash map insertion performance
// Relationships: Benchmarks categorical distribution updates
// Side effects: Creates temporary FeatureInfo for benchmarking
func BenchmarkFeatureInfoAddValueString(b *testing.B) {
	fi := NewFeatureInfo("test", StringFeature, "nominal")
	values := []string{"value1", "value2", "value3", "value4", "value5"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		fi.AddValue(values[i%len(values)])
	}
}

// BenchmarkFeatureInfoAddValueNumerical benchmarks the AddValue method for numerical features.
//
// Args: *testing.B for benchmarking
// Performance: Measures O(1) insertion with lazy cache invalidation
// Relationships: Benchmarks numerical distribution updates
// Side effects: Creates temporary FeatureInfo for benchmarking
func BenchmarkFeatureInfoAddValueNumerical(b *testing.B) {
	fi := NewFeatureInfo("test", IntegerFeature, "numeric")
	values := []int64{10, 20, 30, 40, 50}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		fi.AddValue(values[i%len(values)])
	}
}

// BenchmarkFeatureInfoGetRangeCount benchmarks the GetRangeCount method.
//
// Args: *testing.B for benchmarking
// Performance: Measures O(log n) binary search performance with cache management
// Relationships: Benchmarks range query performance on numerical distributions
// Side effects: Creates temporary FeatureInfo with data for benchmarking
func BenchmarkFeatureInfoGetRangeCount(b *testing.B) {
	fi := NewFeatureInfo("test", FloatFeature, "numeric")

	// Pre-populate with data
	for i := 0; i < 1000; i++ {
		fi.AddValue(float64(i))
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Query different ranges
		min := float64(i % 800)
		max := min + 100
		fi.GetRangeCount(min, max)
	}
}

// Note: Tests for invalid types that cause logger.Fatal() are not included
// as they would exit the test process. These error paths are covered by
// integration tests and manual testing.

// TestNumericalDistributionGetCountInvalidType tests GetCount with invalid types.
func TestNumericalDistributionGetCountInvalidType(t *testing.T) {
	nd := NewNumericalDistribution()
	nd.AddValue(int64(10))

	// Test getting count for invalid type (should return 0)
	count := nd.GetCount("invalid")
	if count != 0 {
		t.Errorf("Expected 0 for invalid type, got %d", count)
	}
}

// TestFeatureInfoGetUniqueValueCountEdgeCases tests GetUniqueValueCount edge cases.
func TestFeatureInfoGetUniqueValueCountEdgeCases(t *testing.T) {
	// Test with string feature
	fi := NewFeatureInfo("test", StringFeature, "string")

	// Empty feature should have 0 unique values
	if count := fi.GetUniqueValueCount(); count != 0 {
		t.Errorf("Expected 0 unique values for empty string feature, got %d", count)
	}

	// Test with numerical feature
	fiNum := NewFeatureInfo("test_num", IntegerFeature, "integer")

	// Empty numerical feature should have 0 unique values
	if count := fiNum.GetUniqueValueCount(); count != 0 {
		t.Errorf("Expected 0 unique values for empty numerical feature, got %d", count)
	}
}

// TestFeatureInfoGetTotalSamplesEdgeCases tests GetTotalSamples edge cases.
func TestFeatureInfoGetTotalSamplesEdgeCases(t *testing.T) {
	// Test with string feature
	fi := NewFeatureInfo("test", StringFeature, "string")

	// Empty feature should have 0 total samples
	if count := fi.GetTotalSamples(); count != 0 {
		t.Errorf("Expected 0 total samples for empty string feature, got %d", count)
	}

	// Test with numerical feature
	fiNum := NewFeatureInfo("test_num", IntegerFeature, "integer")

	// Empty numerical feature should have 0 total samples
	if count := fiNum.GetTotalSamples(); count != 0 {
		t.Errorf("Expected 0 total samples for empty numerical feature, got %d", count)
	}
}

// TestFeatureInfoGetRangeCountEdgeCases tests GetRangeCount edge cases.
func TestFeatureInfoGetRangeCountEdgeCases(t *testing.T) {
	// Test with empty numerical feature
	fiNum := NewFeatureInfo("test_num", IntegerFeature, "integer")

	count := fiNum.GetRangeCount(1.0, 10.0)
	if count != 0 {
		t.Errorf("Expected 0 for empty numerical feature range count, got %d", count)
	}

	// Note: Testing GetRangeCount with string features is not included
	// as it calls logger.Fatal() and would exit the test process.
}

// TestFeatureInfoGetSortedNumericalValuesEdgeCases tests GetSortedNumericalValues edge cases.
func TestFeatureInfoGetSortedNumericalValuesEdgeCases(t *testing.T) {
	// Test with empty numerical feature
	fiNum := NewFeatureInfo("test_num", IntegerFeature, "integer")

	values := fiNum.GetSortedNumericalValues()
	if len(values) != 0 {
		t.Errorf("Expected empty slice for empty numerical feature, got %d values", len(values))
	}

	// Note: Testing GetSortedNumericalValues with string features is not included
	// as it calls logger.Fatal() and would exit the test process.
}

// Note: Testing NewFeatureInfo with unknown feature types is not included
// as it calls logger.Fatal() and would exit the test process.

// TestFeatureInfoGetMostCommonValueEdgeCases tests GetMostCommonValue edge cases.
func TestFeatureInfoGetMostCommonValueEdgeCases(t *testing.T) {
	// Test with empty string feature
	fi := NewFeatureInfo("test", StringFeature, "string")

	value, count := fi.GetMostCommonValue()
	if value != nil || count != 0 {
		t.Errorf("Expected nil value and 0 count for empty string feature, got %v and %d", value, count)
	}

	// Test with empty numerical feature
	fiNum := NewFeatureInfo("test_num", IntegerFeature, "integer")

	value, count = fiNum.GetMostCommonValue()
	if value != nil || count != 0 {
		t.Errorf("Expected nil value and 0 count for empty numerical feature, got %v and %d", value, count)
	}
}
