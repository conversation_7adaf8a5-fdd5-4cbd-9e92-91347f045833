// Package features provides optimal feature distribution storage for Mulberri.
//
// Combines the best aspects of all distribution approaches while maintaining simplicity.
// Fixes float64 map key precision issues using string representations while preserving
// efficient range query capabilities for C4.5 decision tree split evaluation.
//
// Security: No sensitive data storage, thread-safe for concurrent read access
// Performance: O(1) insertions, O(log n) range queries with lazy sorting
package features

import (
	"fmt"
	"sort"
	"strconv"

	"github.com/berrijam/mulberri/internal/utils/logger"
)

// FeatureType represents the core feature types supported by Mulberri.
//
// Args: None (enum type)
// Constraints: Must be one of IntegerFeature, FloatFeature, StringFeature
// Security: No sensitive data handling
// Relationships: Maps to handle_as values from YAML configuration
// Side effects: Determines internal data processing and storage types
type FeatureType int

const (
	IntegerFeature FeatureType = iota // Converted to int64 internally
	FloatFeature                      // Converted to float64 internally
	StringFeature                     // Stored as string internally
)

// String returns the string representation of FeatureType.
//
// Args: None (method receiver)
// Returns: string representation of feature type
// Security: No sensitive data exposure
func (ft FeatureType) String() string {
	switch ft {
	case IntegerFeature:
		return "integer"
	case FloatFeature:
		return "float"
	case StringFeature:
		return "string"
	default:
		return "unknown"
	}
}

// NumericalDistribution handles int64 and float64 values efficiently for C4.5 algorithm.
//
// Args: Stores numerical values using string keys to avoid float precision issues
// Constraints: Only accepts int64 and float64 values, maintains sorted cache lazily
// Security: No sensitive data, safe for concurrent reads after initialization
// Performance: O(1) insertions via hash map, O(log n) range queries via binary search
// Relationships: Used by FeatureInfo for IntegerFeature and FloatFeature types
// Side effects: Lazy computation rebuilds sorted cache when range operations needed
//
// Fixes float64 map key precision errors by using string representations while
// maintaining ability to perform efficient range queries for split evaluation.
type NumericalDistribution struct {
	valueToCount map[string]int // String representation -> count (avoids float key issues)
	sortedValues []float64      // Cached sorted values for range queries (lazily computed)
	sortedDirty  bool           // Flag to indicate if sorted cache needs refresh
	totalCount   int            // Cached total count for performance
}

// NewNumericalDistribution creates a new numerical distribution for C4.5 split evaluation.
//
// Args: None
//
// Returns: initialized NumericalDistribution with empty maps and clean cache
// Performance: O(1) initialization with zero memory allocations for data
// Relationships: Used by NewFeatureInfo for numerical feature types
// Side effects: Allocates memory for internal maps and slices
func NewNumericalDistribution() *NumericalDistribution {
	return &NumericalDistribution{
		valueToCount: make(map[string]int),
		sortedValues: make([]float64, 0),
		sortedDirty:  false,
		totalCount:   0,
	}
}

// AddValue adds a numerical value to distribution avoiding float64 map key precision issues.
//
// Args:
// - value: int64 or float64 numerical value to add (other types cause fatal error)
//
// Performance: O(1) hash map insertion, marks sorted cache dirty for lazy rebuild
// Relationships: Called during dataset loading for each numerical feature sample
// Side effects: Updates internal counts, invalidates sorted cache, logs fatal on invalid type
//
// Example: dist.AddValue(int64(25)) or dist.AddValue(float64(23.7))
func (nd *NumericalDistribution) AddValue(value interface{}) {
	var strKey string

	switch v := value.(type) {
	case int64:
		strKey = strconv.FormatInt(v, 10) // Use integer string representation
	case float64:
		strKey = strconv.FormatFloat(v, 'g', -1, 64) // Use clean float representation
	default:
		logger.Fatal(fmt.Sprintf("unsupported numerical value type: %T", value))
		return
	}

	// Update count using string key (avoids float precision issues)
	nd.valueToCount[strKey]++
	nd.totalCount++
	nd.sortedDirty = true // Mark sorted cache as dirty
}

// GetCount returns the count for a specific numerical value.
//
// Args:
// - value: int64 or float64 value to lookup (other types return 0)
//
// Returns: count of occurrences, 0 if value not found or invalid type
// Performance: O(1) hash map lookup using string key representation
// Relationships: Used by split evaluation algorithms for count queries
func (nd *NumericalDistribution) GetCount(value interface{}) int {
	var strKey string

	switch v := value.(type) {
	case int64:
		strKey = strconv.FormatInt(v, 10)
	case float64:
		strKey = strconv.FormatFloat(v, 'g', -1, 64)
	default:
		return 0
	}

	return nd.valueToCount[strKey]
}

// GetSortedValues returns sorted numerical values for range operations.
//
// Args: None
//
// Returns: copy of sorted values slice (safe for external modification)
// Performance: O(n log n) if cache dirty, O(n) copy operation if cache clean
// Relationships: Used by range query operations and split threshold calculations
// Side effects: Lazily rebuilds sorted cache if dirty, prevents external modification
//
// Lazily computes and caches the sorted slice for performance optimization.
func (nd *NumericalDistribution) GetSortedValues() []float64 {
	if nd.sortedDirty || len(nd.sortedValues) == 0 {
		nd.refreshSortedCache()
	}

	// Return copy to prevent external modification
	result := make([]float64, len(nd.sortedValues))
	copy(result, nd.sortedValues)
	return result
}

// GetRangeCount returns count of values in range [min, max] using binary search.
//
// Args:
// - min: minimum value (inclusive range bound)
// - max: maximum value (inclusive range bound, uses epsilon for float precision)
//
// Returns: count of values within specified range
// Performance: O(log n) binary search on sorted cache, O(n log n) if cache rebuild needed
// Relationships: Used by C4.5 split evaluation for threshold-based splits
// Side effects: May trigger sorted cache rebuild if dirty
//
// Example: dist.GetRangeCount(20.0, 50.0) returns count of values between 20 and 50
func (nd *NumericalDistribution) GetRangeCount(min, max float64) int {
	if nd.sortedDirty || len(nd.sortedValues) == 0 {
		nd.refreshSortedCache()
	}

	// Binary search for range bounds - O(log n)
	startIdx := sort.SearchFloat64s(nd.sortedValues, min)
	endIdx := sort.SearchFloat64s(nd.sortedValues, max+1e-10) // Small epsilon for inclusive upper bound

	return endIdx - startIdx
}

// refreshSortedCache rebuilds the sorted values cache from the string-keyed map.
//
// Args: None (internal method)
//
// Performance: O(n log n) for parsing strings and sorting, O(n) memory allocation
// Relationships: Called internally by range query methods when cache is dirty
// Side effects: Rebuilds sortedValues slice, resets sortedDirty flag, allocates memory
//
// Maintains distribution by adding each value 'count' times to preserve frequency.
func (nd *NumericalDistribution) refreshSortedCache() {
	nd.sortedValues = nd.sortedValues[:0] // Reset slice but keep capacity

	// Extract all unique values
	for strKey, count := range nd.valueToCount {
		if floatVal, err := strconv.ParseFloat(strKey, 64); err == nil {
			// Add each value 'count' times to maintain distribution
			for i := 0; i < count; i++ {
				nd.sortedValues = append(nd.sortedValues, floatVal)
			}
		}
	}

	// Sort for binary search operations
	sort.Float64s(nd.sortedValues)
	nd.sortedDirty = false
}

// GetUniqueCount returns the number of unique values in the distribution.
//
// Args: None
//
// Returns: count of unique values (distinct string keys in map)
// Performance: O(1) using map length
// Relationships: Used for feature cardinality analysis and validation
func (nd *NumericalDistribution) GetUniqueCount() int {
	return len(nd.valueToCount)
}

// GetTotalCount returns the total number of values added to the distribution.
//
// Args: None
//
// Returns: total count of all values (cached for performance)
// Performance: O(1) using cached counter
// Relationships: Used for statistical calculations and split evaluation
func (nd *NumericalDistribution) GetTotalCount() int {
	return nd.totalCount
}

// FeatureInfo holds metadata about a single feature from training data.
//
// Args: Created during data loading process with type-specific distributions
// Security: Contains training data distribution (no PII directly)
// Performance: O(1) lookups for metadata, type-specific performance for operations
// Relationships: Created during data loading, used throughout training process
// Side effects: Distribution updates affect memory usage based on feature cardinality
//
// Uses type-specific distributions for optimal performance and correctness.
// Only one distribution field is used based on Type to avoid memory waste.
type FeatureInfo struct {
	Name         string      // Feature name (CSV column header)
	Type         FeatureType // Internal processing type (integer/float/string)
	OriginalType string      // Original YAML type specification (nominal/numeric/etc.)

	// Type-specific distributions (only one will be used based on Type)
	CategoricalDistribution map[string]int         // For StringFeature only
	NumericalDistribution   *NumericalDistribution // For IntegerFeature and FloatFeature only
}

// NewFeatureInfo creates a new FeatureInfo with appropriate distribution type.
//
// Args:
// - name: Feature name (must be non-empty CSV column header)
// - featureType: Internal processing type (IntegerFeature/FloatFeature/StringFeature)
// - originalType: Original YAML type specification (nominal/numeric/datetime/etc.)
//
// Returns: initialized FeatureInfo with type-appropriate distribution
// Security: No validation of name content (assumes pre-validated by loader)
// Relationships: Used by data loading process to create feature metadata
// Side effects: Allocates memory for appropriate distribution type, logs fatal on invalid type
//
// Example: NewFeatureInfo("age", IntegerFeature, "numeric")
func NewFeatureInfo(name string, featureType FeatureType, originalType string) *FeatureInfo {
	fi := &FeatureInfo{
		Name:         name,
		Type:         featureType,
		OriginalType: originalType,
	}

	// Initialize appropriate distribution based on feature type
	switch featureType {
	case StringFeature:
		fi.CategoricalDistribution = make(map[string]int)
	case IntegerFeature, FloatFeature:
		fi.NumericalDistribution = NewNumericalDistribution()
	default:
		logger.Fatal(fmt.Sprintf("unsupported feature type: %v", featureType))
	}

	return fi
}

// IsNumerical returns true if the feature type is numeric (int or float).
//
// Args: None (method receiver)
//
// Returns: true for IntegerFeature or FloatFeature, false otherwise
// Performance: O(1) type check operation
// Relationships: Used by splitting algorithms to determine strategy (threshold vs value-based)
func (fi *FeatureInfo) IsNumerical() bool {
	return fi.Type == IntegerFeature || fi.Type == FloatFeature
}

// IsCategorical returns true if the feature type is categorical (string).
//
// Args: None (method receiver)
//
// Returns: true for StringFeature, false otherwise
// Performance: O(1) type check operation
// Relationships: Used for categorical splitting and missing value handling
func (fi *FeatureInfo) IsCategorical() bool {
	return fi.Type == StringFeature
}

// AddValue adds a value to the appropriate distribution based on feature type.
//
// Args:
// - value: Feature value to add (string for StringFeature, int64/float64 for numerical)
//
// Performance: O(1) for categorical, O(1) for numerical with lazy cache invalidation
// Relationships: Called during Dataset loading for each training sample
// Side effects: Updates distribution, may invalidate numerical cache, logs fatal on type mismatch
//
// Example: fi.AddValue("red") or fi.AddValue(int64(25))
func (fi *FeatureInfo) AddValue(value interface{}) {
	switch fi.Type {
	case StringFeature:
		if strVal, ok := value.(string); ok {
			fi.CategoricalDistribution[strVal]++
		} else {
			logger.Fatal(fmt.Sprintf("expected string value for string feature, got: %T", value))
		}
	case IntegerFeature, FloatFeature:
		fi.NumericalDistribution.AddValue(value)
	default:
		logger.Fatal(fmt.Sprintf("unsupported feature type: %v", fi.Type))
	}
}

// GetUniqueValueCount returns the number of unique values for this feature.
//
// Args: None (method receiver)
//
// Returns: count of unique values in appropriate distribution
// Performance: O(1) operation using map/distribution length
// Relationships: Used for feature cardinality analysis and splitting decisions
// Side effects: Logs fatal on unsupported feature type
func (fi *FeatureInfo) GetUniqueValueCount() int {
	switch fi.Type {
	case StringFeature:
		return len(fi.CategoricalDistribution)
	case IntegerFeature, FloatFeature:
		return fi.NumericalDistribution.GetUniqueCount()
	default:
		logger.Fatal(fmt.Sprintf("unsupported feature type: %v", fi.Type))
		return 0
	}
}

// GetTotalSamples returns the total number of samples for this feature.
//
// Args: None (method receiver)
//
// Returns: total count of all values across appropriate distribution
// Performance: O(n) for categorical (map value summation), O(1) for numerical (cached)
// Relationships: Used for statistical calculations and validation
// Side effects: Logs fatal on unsupported feature type
func (fi *FeatureInfo) GetTotalSamples() int {
	switch fi.Type {
	case StringFeature:
		total := 0
		for _, count := range fi.CategoricalDistribution {
			total += count
		}
		return total
	case IntegerFeature, FloatFeature:
		return fi.NumericalDistribution.GetTotalCount()
	default:
		logger.Fatal(fmt.Sprintf("unsupported feature type: %v", fi.Type))
		return 0
	}
}

// GetMostCommonValue returns the most frequently occurring value and its count.
//
// Args: None (method receiver)
//
// Returns:
// - interface{}: most common value (nil if no values, appropriate type for feature)
// - int: count of occurrences (0 if no values recorded)
//
// Performance: O(n) iteration through appropriate distribution
// Relationships: Used for missing value imputation strategies
// Side effects: Logs fatal on unsupported feature type, read-only operation on distributions
func (fi *FeatureInfo) GetMostCommonValue() (interface{}, int) {
	var mostCommon interface{}
	maxCount := 0

	switch fi.Type {
	case StringFeature:
		for value, count := range fi.CategoricalDistribution {
			if count > maxCount {
				maxCount = count
				mostCommon = value
			}
		}
	case IntegerFeature, FloatFeature:
		for strValue, count := range fi.NumericalDistribution.valueToCount {
			if count > maxCount {
				maxCount = count
				// Convert back to appropriate type
				if fi.Type == IntegerFeature {
					if intVal, err := strconv.ParseInt(strValue, 10, 64); err == nil {
						mostCommon = intVal
					}
				} else {
					if floatVal, err := strconv.ParseFloat(strValue, 64); err == nil {
						mostCommon = floatVal
					}
				}
			}
		}
	default:
		logger.Fatal(fmt.Sprintf("unsupported feature type: %v", fi.Type))
	}

	return mostCommon, maxCount
}

// GetRangeCount returns count of numerical values in range [min, max].
//
// Args:
// - min: minimum value (inclusive range bound)
// - max: maximum value (inclusive range bound)
//
// Returns: count of values within specified range
// Constraints: Only valid for numerical features (IntegerFeature/FloatFeature)
// Performance: O(log n) binary search on numerical distribution
// Relationships: Used by C4.5 split evaluation for threshold-based splits
// Side effects: Logs fatal if called on non-numerical feature, may rebuild numerical cache
//
// Example: fi.GetRangeCount(20.0, 50.0) returns count between 20 and 50
func (fi *FeatureInfo) GetRangeCount(min, max float64) int {
	if !fi.IsNumerical() {
		logger.Fatal("GetRangeCount only valid for numerical features")
		return 0
	}

	return fi.NumericalDistribution.GetRangeCount(min, max)
}

// GetSortedNumericalValues returns sorted values for range operations.
//
// Args: None (method receiver)
//
// Returns: copy of sorted numerical values (safe for external modification)
// Constraints: Only valid for numerical features (IntegerFeature/FloatFeature)
// Performance: O(n log n) if cache rebuild needed, O(n) for copy operation
// Relationships: Used by split evaluation algorithms for threshold calculations
// Side effects: Logs fatal if called on non-numerical feature, may rebuild numerical cache
//
// Example: values := fi.GetSortedNumericalValues() for split threshold analysis
func (fi *FeatureInfo) GetSortedNumericalValues() []float64 {
	if !fi.IsNumerical() {
		logger.Fatal("GetSortedNumericalValues only valid for numerical features")
		return nil
	}

	return fi.NumericalDistribution.GetSortedValues()
}
